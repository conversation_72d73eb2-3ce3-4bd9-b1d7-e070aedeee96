'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { StatsCard } from '@/components/dashboard/StatsCard'
import { ProjectChart } from '@/components/dashboard/ProjectChart'
import { Card } from '@/components/ui/Card'
import { But<PERSON> } from '@/components/ui/Button'
import { DashboardWrapper } from '@/components/DashboardWrapper'
import Link from 'next/link'
import { api } from '@/lib/api'

export default function Dashboard() {
  const router = useRouter()
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthLoading, setIsAuthLoading] = useState(true)
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalEmployees: 0,
    totalHours: 0,
    pendingTasks: 0
  })

  const [recentProjects, setRecentProjects] = useState<{ id: number; name: string; status: string; progress: number; client: string; dueDate: string }[]>([])

  // Vérification de l'authentification
  useEffect(() => {
    const token = localStorage.getItem('auth_token')
    const userData = localStorage.getItem('user_data')

    if (!token || !userData) {
      router.push('/auth/login')
      return
    }

    try {
      setUser(JSON.parse(userData))
    } catch (error) {
      console.error('Erreur parsing user data:', error)
      router.push('/auth/login')
      return
    }

    setIsAuthLoading(false)
  }, [router])

  useEffect(() => {
    if (isAuthLoading) return // Attendre la vérification d'auth
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)

        // Mettre à jour le token dans l'API client
        const token = localStorage.getItem('auth_token')
        if (token) {
          api.setToken(token)
        }

        // Fetch stats from API
        const [projects, employees, stats] = await Promise.all([
          api.getProjects(),
          api.getEmployees(),
          api.getDashboardStats()
        ])
        
        if (stats) {
          setStats({
            totalProjects: stats.totalProjects || projects?.length || 0,
            activeProjects: stats.activeProjects || projects?.filter(p => p.status === 'En cours').length || 0,
            completedProjects: stats.completedProjects || projects?.filter(p => p.status === 'Terminé').length || 0,
            totalEmployees: stats.totalEmployees || employees?.length || 0,
            totalHours: stats.totalHours || 0,
            pendingTasks: stats.pendingTasks || 0
          })
        } else {
          // If dashboard stats endpoint is not available, use projects and employees data
          setStats({
            totalProjects: projects?.length || 0,
            activeProjects: projects?.filter(p => p.status === 'En cours').length || 0,
            completedProjects: projects?.filter(p => p.status === 'Terminé').length || 0,
            totalEmployees: employees?.length || 0,
            totalHours: 0,
            pendingTasks: 0
          })
        }
        
        // Map projects to the required format for recent projects
        if (projects && projects.length > 0) {
          const recentProjectsData = projects
            .slice(0, 4)
            .map(project => ({
              id: project.id,
              name: project.name,
              status: project.status || 'En cours',
              progress: project.progress || 0,
              client: project.client_name || 'Client',
              dueDate: project.end_date ? new Date(project.end_date).toLocaleDateString('fr-FR') : 'Non défini'
            }))
          setRecentProjects(recentProjectsData)
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [isAuthLoading])

  const handleLogout = () => {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
    router.push('/auth/login')
  }

  // Affichage de chargement pendant la vérification d'auth
  if (isAuthLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <DashboardWrapper>
      <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-4">
            <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-xl">🏗️</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tableau de bord ORBIS</h1>
              <p className="text-gray-600 mt-1 text-sm md:text-base">
                Bienvenue, {user?.first_name} {user?.last_name} - Vue d&apos;ensemble des projets et activités
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className="text-sm text-gray-600 hidden sm:block">
              {user?.email}
            </div>
            <Button
              variant="primary"
              className="flex items-center"
              icon={
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              }
            >
              Nouveau Projet
            </Button>
            <Button
              variant="outline"
              onClick={handleLogout}
              className="flex items-center text-red-600 border-red-300 hover:bg-red-50"
            >
              Déconnexion
            </Button>
          </div>
        </div>
      </div>

      {/* Welcome Card */}
      <Card className="p-6 bg-gradient-to-r from-primary-50 to-white border-l-4 border-primary-500">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-xl font-bold text-primary-800">Bienvenue sur ORBIS Suivi Travaux</h2>
            <p className="mt-1 text-primary-700">Votre plateforme de gestion de projets BTP</p>
          </div>
          <div className="hidden md:block">
            <span className="text-4xl">🏗️</span>
          </div>
        </div>
      </Card>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
        <StatsCard
          title="Projets Totaux"
          value={stats.totalProjects}
          icon={
            <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5" />
            </svg>
          }
          trend={{ value: 12, isPositive: true }}
          className="animate-fadeIn"
          isLoading={isLoading}
        />
        <StatsCard
          title="Projets Actifs"
          value={stats.activeProjects}
          icon={
            <svg className="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          }
          trend={{ value: 8, isPositive: true }}
          className="animate-fadeIn delay-100"
          isLoading={isLoading}
        />
        <StatsCard
          title="Projets Terminés"
          value={stats.completedProjects}
          icon={
            <svg className="w-6 h-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
            </svg>
          }
          trend={{ value: 5, isPositive: true }}
          className="animate-fadeIn delay-200"
          isLoading={isLoading}
        />
        <StatsCard
          title="Employés Actifs"
          value={stats.totalEmployees}
          icon={
            <svg className="w-6 h-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          }
          trend={{ value: 2, isPositive: true }}
          className="animate-fadeIn delay-300"
          isLoading={isLoading}
        />
        <StatsCard
          title="Heures Travaillées"
          value={stats.totalHours}
          icon={
            <svg className="w-6 h-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
          trend={{ value: 120, isPositive: true }}
          className="animate-fadeIn delay-400"
          isLoading={isLoading}
        />
        <StatsCard
          title="Tâches Pendantes"
          value={stats.pendingTasks}
          icon={
            <svg className="w-6 h-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
          trend={{ value: 3, isPositive: false }}
          className="animate-fadeIn delay-500"
          isLoading={isLoading}
        />
      </div>

      {/* Charts and Recent Projects */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Chart */}
        <Card className="p-0 overflow-hidden">
          <div className="p-5 border-b border-gray-100">
            <h2 className="text-lg font-semibold text-gray-800">Évolution des Projets</h2>
            <p className="text-sm text-gray-500 mt-1">Progression mensuelle</p>
          </div>
          <div className="p-5">
            {isLoading ? (
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                <div className="animate-pulse flex space-x-4">
                  <div className="h-16 w-16 bg-gray-200 rounded-full"></div>
                  <div className="space-y-3 flex-1">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-64">
                <ProjectChart />
              </div>
            )}
          </div>
          <div className="px-5 py-3 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
            <span className="text-sm text-gray-600">Dernière mise à jour: aujourd&apos;hui</span>
            <Link href="/reports" className="text-sm font-medium text-primary-600 hover:text-primary-800 transition-colors">
              Voir tous les rapports
            </Link>
          </div>
        </Card>

        {/* Recent Projects */}
        <Card className="p-0 overflow-hidden">
          <div className="p-5 border-b border-gray-100 flex justify-between items-center">
            <div>
              <h2 className="text-lg font-semibold text-gray-800">Projets Récents</h2>
              <p className="text-sm text-gray-500 mt-1">Dernières activités</p>
            </div>
            <Link href="/projects" className="text-sm font-medium text-primary-600 hover:text-primary-800 transition-colors">
              Voir tous
            </Link>
          </div>
          <div className="divide-y divide-gray-100">
            {isLoading ? (
              <div className="p-5 space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="animate-pulse flex items-center space-x-4">
                    <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded"></div>
                      <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div>
                {recentProjects.map((project: { id: number; name: string; status: string; progress: number; client: string; dueDate: string }) => (
                  <div key={project.id} className="p-4 hover:bg-gray-50 transition-colors duration-150">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${project.status === 'En cours' ? 'bg-primary-100 text-primary-700' : project.status === 'Terminé' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}`}>
                          {project.status === 'En cours' ? (
                            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                          ) : project.status === 'Terminé' ? (
                            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          ) : (
                            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          )}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <Link href={`/projects/${project.id}`} className="text-sm font-medium text-gray-900 hover:text-primary-600 block">
                          {project.name}
                        </Link>
                        <div className="flex flex-col sm:flex-row sm:items-center mt-1 text-xs text-gray-500 space-y-1 sm:space-y-0 sm:space-x-4">
                          <span className="flex items-center">
                            <svg className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            {project.client}
                          </span>
                          <span className="flex items-center">
                            <svg className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            Échéance: {project.dueDate}
                          </span>
                        </div>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${project.status === 'En cours' ? 'bg-primary-100 text-primary-800' : project.status === 'Terminé' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                        {project.status}
                      </span>
                    </div>
                    <div className="mt-3">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-600">Progrès</span>
                        <span className="font-medium">{project.progress}%</span>
                      </div>
                      <div className="mt-1 w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className={`h-1.5 rounded-full ${project.status === 'En cours' ? 'bg-primary-600' : project.status === 'Terminé' ? 'bg-green-600' : 'bg-yellow-500'}`}
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-0 overflow-hidden">
        <div className="p-5 border-b border-gray-100">
          <h2 className="text-lg font-semibold text-gray-800">Actions Rapides</h2>
          <p className="text-sm text-gray-500 mt-1">Gérer facilement vos ressources</p>
        </div>
        <div className="p-5">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <Button 
              variant="outline" 
              fullWidth
              className="flex-col h-auto py-6"
              icon={
                <svg className="w-6 h-6 mb-2 text-primary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              }
              iconPosition="left"
            >
              <div className="flex flex-col items-center">
                <span className="font-medium">Nouveau Projet</span>
                <span className="text-xs text-gray-500 mt-1">Créer un projet</span>
              </div>
            </Button>
            
            <Button 
              variant="outline" 
              fullWidth
              className="flex-col h-auto py-6"
              icon={
                <svg className="w-6 h-6 mb-2 text-primary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              }
              iconPosition="left"
            >
              <div className="flex flex-col items-center">
                <span className="font-medium">Ajouter Employé</span>
                <span className="text-xs text-gray-500 mt-1">Nouvel utilisateur</span>
              </div>
            </Button>
            
            <Button 
              variant="outline" 
              fullWidth
              className="flex-col h-auto py-6"
              icon={
                <svg className="w-6 h-6 mb-2 text-primary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              }
              iconPosition="left"
            >
              <div className="flex flex-col items-center">
                <span className="font-medium">Gérer Documents</span>
                <span className="text-xs text-gray-500 mt-1">Ajouter des fichiers</span>
              </div>
            </Button>
            
            <Button 
              variant="outline" 
              fullWidth
              className="flex-col h-auto py-6"
              icon={
                <svg className="w-6 h-6 mb-2 text-primary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0-6l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
                </svg>
              }
              iconPosition="left"
            >
              <div className="flex flex-col items-center">
                <span className="font-medium">Voir Rapports</span>
                <span className="text-xs text-gray-500 mt-1">Statistiques</span>
              </div>
            </Button>
          </div>
        </div>
      </Card>
      </div>
    </DashboardWrapper>
  )
}