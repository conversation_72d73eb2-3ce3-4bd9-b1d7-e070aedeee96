'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardWrapper } from '@/components/DashboardWrapper'
import ModernSidebar from '@/components/ModernSidebar'
import ModernHeader from '@/components/ModernHeader'
import {
  ProjectStatsCard,
  EmployeeStatsCard,
  HoursStatsCard,
  TasksStatsCard
} from '@/components/ModernStatsCard'
import Link from 'next/link'
import { api } from '@/lib/api'

export default function Dashboard() {
  const router = useRouter()
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthLoading, setIsAuthLoading] = useState(true)
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalEmployees: 0,
    totalHours: 0,
    pendingTasks: 0
  })

  const [recentProjects, setRecentProjects] = useState<{ id: number; name: string; status: string; progress: number; client: string; dueDate: string }[]>([])

  // Vérification de l'authentification
  useEffect(() => {
    const token = localStorage.getItem('auth_token')
    const userData = localStorage.getItem('user_data')

    if (!token || !userData) {
      router.push('/auth/login')
      return
    }

    try {
      setUser(JSON.parse(userData))
    } catch (error) {
      console.error('Erreur parsing user data:', error)
      router.push('/auth/login')
      return
    }

    setIsAuthLoading(false)
  }, [router])

  useEffect(() => {
    if (isAuthLoading) return // Attendre la vérification d'auth
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)

        // Mettre à jour le token dans l'API client
        const token = localStorage.getItem('auth_token')
        if (token) {
          api.setToken(token)
        }

        // Fetch stats from API
        const [projects, employees, stats] = await Promise.all([
          api.getProjects(),
          api.getEmployees(),
          api.getDashboardStats()
        ])
        
        if (stats) {
          setStats({
            totalProjects: stats.totalProjects || projects?.length || 0,
            activeProjects: stats.activeProjects || projects?.filter(p => p.status === 'En cours').length || 0,
            completedProjects: stats.completedProjects || projects?.filter(p => p.status === 'Terminé').length || 0,
            totalEmployees: stats.totalEmployees || employees?.length || 0,
            totalHours: stats.totalHours || 0,
            pendingTasks: stats.pendingTasks || 0
          })
        } else {
          // If dashboard stats endpoint is not available, use projects and employees data
          setStats({
            totalProjects: projects?.length || 0,
            activeProjects: projects?.filter(p => p.status === 'En cours').length || 0,
            completedProjects: projects?.filter(p => p.status === 'Terminé').length || 0,
            totalEmployees: employees?.length || 0,
            totalHours: 0,
            pendingTasks: 0
          })
        }
        
        // Map projects to the required format for recent projects
        if (projects && projects.length > 0) {
          const recentProjectsData = projects
            .slice(0, 4)
            .map(project => ({
              id: project.id,
              name: project.name,
              status: project.status || 'En cours',
              progress: project.progress || 0,
              client: project.client_name || 'Client',
              dueDate: project.end_date ? new Date(project.end_date).toLocaleDateString('fr-FR') : 'Non défini'
            }))
          setRecentProjects(recentProjectsData)
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [isAuthLoading])

  const handleLogout = () => {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
    router.push('/auth/login')
  }

  // Affichage de chargement pendant la vérification d'auth
  if (isAuthLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Sidebar */}
        <ModernSidebar user={user} />

        {/* Main Content */}
        <div className="flex-1 lg:ml-0">
          {/* Header */}
          <ModernHeader
            title="Dashboard"
            subtitle={`Bienvenue, ${user?.first_name} ${user?.last_name} - Vue d'ensemble des projets et activités`}
            user={user}
            onLogout={handleLogout}
          />

          {/* Content */}
          <main className="p-6">
            <div className="space-y-8">

              {/* Welcome Section */}
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-8 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold mb-2">Bienvenue sur ORBIS</h2>
                    <p className="text-blue-100 mb-4">Votre plateforme de gestion de projets BTP</p>
                    <div className="flex items-center space-x-4">
                      <button className="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-all duration-200">
                        Nouveau Projet
                      </button>
                      <span className="text-blue-100 text-sm">
                        Dernière connexion: {new Date().toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                  </div>
                  <div className="hidden md:block text-6xl opacity-20">
                    🏗️
                  </div>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <ProjectStatsCard
                  totalProjects={stats.totalProjects}
                  activeProjects={stats.activeProjects}
                />
                <EmployeeStatsCard totalEmployees={stats.totalEmployees} />
                <HoursStatsCard totalHours={stats.totalHours} />
                <TasksStatsCard pendingTasks={stats.pendingTasks} />
              </div>

              {/* Charts and Recent Projects */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Modern Chart Card */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Évolution des Projets</h3>
                        <p className="text-sm text-gray-500 mt-1">Progression mensuelle</p>
                      </div>
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    {isLoading ? (
                      <div className="h-64 flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      </div>
                    ) : (
                      <div className="h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-4xl mb-4">📊</div>
                          <p className="text-gray-600">Graphique interactif</p>
                          <p className="text-sm text-gray-500">Données en temps réel</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Dernière mise à jour: {new Date().toLocaleDateString('fr-FR')}</span>
                      <Link href="/reports" className="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
                        Voir rapports →
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Modern Recent Projects Card */}
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                  <div className="p-6 border-b border-gray-100">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">Projets Récents</h3>
                        <p className="text-sm text-gray-500 mt-1">Dernières activités</p>
                      </div>
                      <div className="p-2 bg-green-100 rounded-lg">
                        <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    {isLoading ? (
                      <div className="space-y-4">
                        {[1, 2, 3].map(i => (
                          <div key={i} className="animate-pulse flex items-center space-x-4">
                            <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-gray-200 rounded"></div>
                              <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {recentProjects.map((project: { id: number; name: string; status: string; progress: number; client: string; dueDate: string }) => (
                          <div key={project.id} className="p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-all duration-200 border border-gray-100">
                            <div className="flex items-center space-x-4">
                              <div className="flex-shrink-0">
                                <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                                  project.status === 'En cours'
                                    ? 'bg-blue-100 text-blue-600'
                                    : project.status === 'Terminé'
                                    ? 'bg-green-100 text-green-600'
                                    : 'bg-yellow-100 text-yellow-600'
                                }`}>
                                  {project.status === 'En cours' ? '🚧' : project.status === 'Terminé' ? '✅' : '⏳'}
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <Link href={`/projects/${project.id}`} className="text-sm font-semibold text-gray-900 hover:text-blue-600 block transition-colors">
                                  {project.name}
                                </Link>
                                <div className="flex items-center mt-1 text-xs text-gray-500 space-x-4">
                                  <span className="flex items-center">
                                    👤 {project.client}
                                  </span>
                                  <span className="flex items-center">
                                    📅 {project.dueDate}
                                  </span>
                                </div>
                                <div className="mt-2">
                                  <div className="flex items-center justify-between text-xs">
                                    <span className="text-gray-600">Progression</span>
                                    <span className="font-medium text-gray-900">{project.progress}%</span>
                                  </div>
                                  <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                                    <div
                                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                                      style={{ width: `${project.progress}%` }}
                                    ></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                    <Link href="/projects" className="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
                      Voir tous les projets →
                    </Link>
                  </div>
                </div>
              </div>
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            {project.client}
                          </span>
                          <span className="flex items-center">
                            <svg className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            Échéance: {project.dueDate}
                          </span>
                        </div>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${project.status === 'En cours' ? 'bg-primary-100 text-primary-800' : project.status === 'Terminé' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                        {project.status}
                      </span>
                    </div>
                    <div className="mt-3">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-600">Progrès</span>
                        <span className="font-medium">{project.progress}%</span>
                      </div>
                      <div className="mt-1 w-full bg-gray-200 rounded-full h-1.5">
                        <div 
                          className={`h-1.5 rounded-full ${project.status === 'En cours' ? 'bg-primary-600' : project.status === 'Terminé' ? 'bg-green-600' : 'bg-yellow-500'}`}
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-0 overflow-hidden">
        <div className="p-5 border-b border-gray-100">
          <h2 className="text-lg font-semibold text-gray-800">Actions Rapides</h2>
          <p className="text-sm text-gray-500 mt-1">Gérer facilement vos ressources</p>
        </div>
        <div className="p-5">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <Button 
              variant="outline" 
              fullWidth
              className="flex-col h-auto py-6"
              icon={
                <svg className="w-6 h-6 mb-2 text-primary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              }
              iconPosition="left"
            >
              <div className="flex flex-col items-center">
                <span className="font-medium">Nouveau Projet</span>
                <span className="text-xs text-gray-500 mt-1">Créer un projet</span>
              </div>
            </Button>
            
            <Button 
              variant="outline" 
              fullWidth
              className="flex-col h-auto py-6"
              icon={
                <svg className="w-6 h-6 mb-2 text-primary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              }
              iconPosition="left"
            >
              <div className="flex flex-col items-center">
                <span className="font-medium">Ajouter Employé</span>
                <span className="text-xs text-gray-500 mt-1">Nouvel utilisateur</span>
              </div>
            </Button>
            
            <Button 
              variant="outline" 
              fullWidth
              className="flex-col h-auto py-6"
              icon={
                <svg className="w-6 h-6 mb-2 text-primary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              }
              iconPosition="left"
            >
              <div className="flex flex-col items-center">
                <span className="font-medium">Gérer Documents</span>
                <span className="text-xs text-gray-500 mt-1">Ajouter des fichiers</span>
              </div>
            </Button>
            
            <Button 
              variant="outline" 
              fullWidth
              className="flex-col h-auto py-6"
              icon={
                <svg className="w-6 h-6 mb-2 text-primary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0-6l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
                </svg>
              }
              iconPosition="left"
            >
              <div className="flex flex-col items-center">
                <span className="font-medium">Voir Rapports</span>
                <span className="text-xs text-gray-500 mt-1">Statistiques</span>
              </div>
            </Button>
          </div>
        </div>
      </Card>
      </div>
    </DashboardWrapper>
  )
}