import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Navbar } from '@/components/layout/Navbar'
import { Sidebar } from '@/components/layout/Sidebar'

// Import Inter font with all weights
const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'ORBIS Suivi Travaux - Gestion de Projets SAAS',
  description: 'Application de suivi et gestion de travaux pour entreprises du BTP',
  viewport: 'width=device-width, initial-scale=1',
  icons: {
    icon: '/favicon.ico',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr" className={inter.variable}>
      <body className={`font-sans antialiased ${inter.className}`}>
        <div className="min-h-screen bg-gray-50">
          <Navbar />
          <div className="flex pt-16">
            <Sidebar />
            <main className="flex-1 transition-all duration-300 lg:ml-64 sm:ml-16 ml-0 p-4 sm:p-6 md:p-8">
              <div className="max-w-7xl mx-auto animate-fade-in">
                {children}
              </div>
            </main>
          </div>
        </div>
      </body>
    </html>
  )
}