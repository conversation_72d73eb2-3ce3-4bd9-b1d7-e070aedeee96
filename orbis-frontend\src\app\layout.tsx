import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

// Import Inter font with all weights
const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'ORBIS Suivi Travaux - Gestion de Projets SAAS',
  description: 'Application de suivi et gestion de travaux pour entreprises du BTP',
  icons: {
    icon: '/favicon.ico',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr">
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  )
}