#!/usr/bin/env python3
"""
Test simple de connexion à la base de données ORBIS
Script utilisant uniquement psycopg2 et asyncpg pour tester la connexion
"""

import asyncio
import sys
from datetime import datetime

# Configuration directe (copiée de config.py)
SUPABASE_URL = "https://dkmyxkkokwuxopahokcd.supabase.co"
DATABASE_URL = "****************************************************************************/postgres"
ASYNC_DATABASE_URL = "****************************************************************************/postgres"


def print_header(title: str):
    """Affiche un en-tête formaté"""
    print(f"\n{'='*50}")
    print(f"🔧 {title}")
    print(f"{'='*50}")


def print_test_result(test_name: str, success: bool, details: str = ""):
    """Affiche le résultat d'un test"""
    status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
    print(f"{status} - {test_name}")
    if details:
        print(f"   📝 {details}")


def test_psycopg2_connection():
    """Test de connexion avec psycopg2"""
    print_header("Test de connexion psycopg2")
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(DATABASE_URL)
        cursor = conn.cursor()
        
        # Test de version
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        
        # Test de base de données actuelle
        cursor.execute("SELECT current_database()")
        db_name = cursor.fetchone()[0]
        
        # Test de comptage des tables
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        table_count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        print_test_result("Connexion psycopg2", True, f"PostgreSQL: {version[:50]}...")
        print_test_result("Base de données", True, f"DB: {db_name}")
        print_test_result("Tables publiques", True, f"{table_count} tables trouvées")
        
        return True
        
    except ImportError:
        print_test_result("Connexion psycopg2", False, "Module psycopg2 non installé")
        return False
    except Exception as e:
        print_test_result("Connexion psycopg2", False, str(e))
        return False


async def test_asyncpg_connection():
    """Test de connexion avec asyncpg"""
    print_header("Test de connexion AsyncPG")
    
    try:
        import asyncpg
        
        conn = await asyncpg.connect(ASYNC_DATABASE_URL)
        
        # Test de version
        version = await conn.fetchval("SELECT version()")
        
        # Test de base de données actuelle
        db_name = await conn.fetchval("SELECT current_database()")
        
        # Test de comptage des tables
        table_count = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        
        # Lister quelques tables
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
            LIMIT 10
        """)
        
        await conn.close()
        
        print_test_result("Connexion AsyncPG", True, f"PostgreSQL: {version[:50]}...")
        print_test_result("Base de données", True, f"DB: {db_name}")
        print_test_result("Tables publiques", True, f"{table_count} tables trouvées")
        
        if tables:
            print("   📋 Quelques tables trouvées:")
            for table in tables:
                print(f"      - {table['table_name']}")
        
        return True
        
    except ImportError:
        print_test_result("Connexion AsyncPG", False, "Module asyncpg non installé")
        return False
    except Exception as e:
        print_test_result("Connexion AsyncPG", False, str(e))
        return False


def test_supabase_api():
    """Test de l'API Supabase avec requests"""
    print_header("Test API Supabase")
    
    try:
        import requests
        
        # Clé anonyme Supabase
        SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRrbXl4a2tva3d1eG9wYWhva2NkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ2MDQzNTEsImV4cCI6MjA1MDE4MDM1MX0.T--dqBJLQpPLYeH8_sE0zL5nWEU96xYH_8_8hKQ2c4w"
        
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        # Test de connexion à l'API
        response = requests.get(f"{SUPABASE_URL}/rest/v1/", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print_test_result("API Supabase", True, f"Status: {response.status_code}")
            return True
        else:
            print_test_result("API Supabase", False, f"Status: {response.status_code}")
            return False
            
    except ImportError:
        print_test_result("API Supabase", False, "Module requests non installé")
        return False
    except Exception as e:
        print_test_result("API Supabase", False, str(e))
        return False


async def check_specific_tables():
    """Vérifier des tables spécifiques de l'application"""
    print_header("Vérification des Tables de l'Application")
    
    expected_tables = [
        'users', 'companies', 'projects', 'employees',
        'suppliers', 'materials', 'budgets', 'invoices',
        'purchase_orders', 'quotes', 'documents'
    ]
    
    try:
        import asyncpg
        
        conn = await asyncpg.connect(ASYNC_DATABASE_URL)
        
        existing_tables = []
        missing_tables = []
        
        for table in expected_tables:
            try:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                existing_tables.append((table, count))
                print_test_result(f"Table {table}", True, f"{count} enregistrements")
            except Exception:
                missing_tables.append(table)
                print_test_result(f"Table {table}", False, "Table non trouvée")
        
        await conn.close()
        
        if missing_tables:
            print(f"\n⚠️  Tables manquantes: {', '.join(missing_tables)}")
            print("💡 Vous devrez peut-être exécuter les migrations Alembic ou le script seed_db.py")
        
        return len(missing_tables) == 0
        
    except ImportError:
        print_test_result("Vérification des tables", False, "Module asyncpg non installé")
        return False
    except Exception as e:
        print_test_result("Vérification des tables", False, str(e))
        return False


def print_configuration():
    """Affiche la configuration"""
    print_header("Configuration de la Base de Données")
    
    print(f"🔗 URL Supabase: {SUPABASE_URL}")
    print(f"🔗 URL Base de données: {DATABASE_URL[:50]}...")
    print(f"🔗 URL Async: {ASYNC_DATABASE_URL[:50]}...")


async def main():
    """Fonction principale de test"""
    print("🚀 ORBIS - Test Simple de Connexion à la Base de Données")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Afficher la configuration
    print_configuration()
    
    # Compteur de tests réussis
    tests_passed = 0
    total_tests = 4
    
    # Tests de connexion
    if test_psycopg2_connection():
        tests_passed += 1
    
    if await test_asyncpg_connection():
        tests_passed += 1
    
    if test_supabase_api():
        tests_passed += 1
    
    if await check_specific_tables():
        tests_passed += 1
    
    # Résumé final
    print_header("Résumé des Tests")
    success_rate = (tests_passed / total_tests) * 100
    
    if tests_passed == total_tests:
        print(f"🎉 TOUS LES TESTS RÉUSSIS! ({tests_passed}/{total_tests}) - {success_rate:.0f}%")
        print("✅ Votre base de données est correctement configurée et accessible.")
    elif tests_passed > 0:
        print(f"⚠️  TESTS PARTIELLEMENT RÉUSSIS ({tests_passed}/{total_tests}) - {success_rate:.0f}%")
        print("🔧 Certaines connexions fonctionnent, vérifiez les erreurs ci-dessus.")
        
        if tests_passed >= 2:
            print("\n💡 Suggestions:")
            print("   - Installez les dépendances manquantes avec: pip install psycopg2-binary asyncpg requests")
            print("   - Exécutez les migrations: alembic upgrade head")
            print("   - Créez les données de test: python seed_db.py")
    else:
        print(f"❌ TOUS LES TESTS ONT ÉCHOUÉ ({tests_passed}/{total_tests}) - {success_rate:.0f}%")
        print("🚨 Problème de configuration de la base de données.")
        print("\n💡 Vérifiez:")
        print("   - La connexion internet")
        print("   - Les credentials Supabase")
        print("   - L'installation des modules Python")
    
    return tests_passed > 0


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
