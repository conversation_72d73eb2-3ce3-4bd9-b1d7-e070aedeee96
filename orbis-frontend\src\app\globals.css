@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-50: 240, 249, 255;
  --primary-100: 224, 242, 254;
  --primary-200: 186, 230, 253;
  --primary-300: 125, 211, 252;
  --primary-400: 56, 189, 248;
  --primary-500: 14, 165, 233;
  --primary-600: 2, 132, 199;
  --primary-700: 3, 105, 161;
  --primary-800: 7, 89, 133;
  --primary-900: 12, 74, 110;
  --primary-950: 8, 47, 73;
}

/* Base Styles */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply text-gray-800 bg-gray-50;
  }

  h1 {
    @apply text-3xl font-bold text-gray-900 mb-6;
  }

  h2 {
    @apply text-2xl font-bold text-gray-800 mb-4;
  }

  h3 {
    @apply text-xl font-semibold text-gray-800 mb-3;
  }

  h4 {
    @apply text-lg font-semibold text-gray-800 mb-2;
  }
}

/* Component Styles */
@layer components {
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-card hover:shadow-card-hover transition-shadow duration-300 overflow-hidden;
  }

  .card-header {
    @apply p-4 border-b border-gray-100 flex items-center justify-between;
  }

  .card-body {
    @apply p-4;
  }

  .card-footer {
    @apply p-4 bg-gray-50 border-t border-gray-100;
  }

  .form-input {
    @apply rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-25;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .badge {
    @apply inline-flex items-center px-2 py-0.5 rounded text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
}

/* Custom scrollbar */
.scrollbar-thin::-webkit-scrollbar {
  width: 5px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Animation Delay Utilities */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: #fff;
    color: #000;
  }
  
  .print-break-inside-avoid {
    break-inside: avoid;
  }
}