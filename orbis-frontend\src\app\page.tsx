import Link from 'next/link'
import { ClientWrapper } from '@/components/ClientWrapper'

export default function Home() {
  return (
    <ClientWrapper>
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-blue-100 mb-6">
            <span className="text-3xl">🏗️</span>
          </div>

          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            ORBIS Suivi Travaux
          </h1>

          <p className="text-lg text-gray-600 mb-8">
            Application de gestion de projets pour entreprises du BTP
          </p>

          <div className="space-y-4">
            <Link
              href="/auth/login"
              className="block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Se connecter
            </Link>

            <Link
              href="/auth/register"
              className="block w-full border border-blue-600 text-blue-600 py-3 px-6 rounded-lg hover:bg-blue-50 transition-colors"
            >
              Créer un compte
            </Link>
          </div>

          <div className="mt-8 text-sm text-gray-500">
            <p>✅ Backend API : http://localhost:8000</p>
            <p>✅ Frontend : http://localhost:3000</p>
          </div>
        </div>
      </div>
    </ClientWrapper>
  )
}