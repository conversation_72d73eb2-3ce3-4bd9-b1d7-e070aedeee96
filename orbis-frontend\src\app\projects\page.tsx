'use client'

import { useState, useEffect } from 'react'
import { ProjectCard } from '@/components/projects/ProjectCard'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import Link from 'next/link'
import { api } from '@/lib/api'

export default function Projects() {
  const [projects, setProjects] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true)
        const data = await api.getProjects()
        setProjects(data)
        setError(null)
      } catch (err) {
        console.error('Error fetching projects:', err)
        setError('Erreur lors du chargement des projets')
      } finally {
        setLoading(false)
      }
    }

    fetchProjects()
  }, [])

  const filteredProjects = projects.filter((project: any) => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterStatus === 'all' || project.status === filterStatus
    return matchesSearch && matchesFilter
  })

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-lg text-gray-600">Chargement des projets...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-4">{error}</div>
          <Button onClick={() => window.location.reload()}>
            Réessayer
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestion des Projets</h1>
          <p className="text-gray-600 mt-1">Gérez et suivez tous vos projets de construction</p>
        </div>
        <Link href="/projects/create">
          <Button className="bg-blue-600 hover:bg-blue-700">
            Nouveau Projet
          </Button>
        </Link>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Rechercher un projet..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={filterStatus === 'all' ? 'primary' : 'outline'}
              onClick={() => setFilterStatus('all')}
            >
              Tous
            </Button>
            <Button
              variant={filterStatus === 'En cours' ? 'primary' : 'outline'}
              onClick={() => setFilterStatus('En cours')}
            >
              En cours
            </Button>
            <Button
              variant={filterStatus === 'En attente' ? 'primary' : 'outline'}
              onClick={() => setFilterStatus('En attente')}
            >
              En attente
            </Button>
            <Button
              variant={filterStatus === 'Terminé' ? 'primary' : 'outline'}
              onClick={() => setFilterStatus('Terminé')}
            >
              Terminés
            </Button>
          </div>
        </div>
      </Card>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProjects.map((project: any) => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </div>

      {/* Empty State */}
      {filteredProjects.length === 0 && (
        <Card className="p-12 text-center">
          <div className="text-gray-400 text-6xl mb-4">📋</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Aucun projet trouvé</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterStatus !== 'all' 
              ? 'Aucun projet ne correspond à vos critères de recherche.'
              : 'Vous n\'avez pas encore de projets. Créez votre premier projet pour commencer.'
            }
          </p>
          <Link href="/projects/create">
            <Button className="bg-blue-600 hover:bg-blue-700">
              Créer un Projet
            </Button>
          </Link>
        </Card>
      )}

      {/* Stats Summary */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Résumé</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{projects.length}</div>
            <div className="text-sm text-gray-600">Total Projets</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {projects.filter((p: any) => p.status === 'En cours').length}
            </div>
            <div className="text-sm text-gray-600">En Cours</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {projects.filter((p: any) => p.status === 'En attente').length}
            </div>
            <div className="text-sm text-gray-600">En Attente</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">
              {projects.filter((p: any) => p.status === 'Terminé').length}
            </div>
            <div className="text-sm text-gray-600">Terminés</div>
          </div>
        </div>
      </Card>
    </div>
  )
}