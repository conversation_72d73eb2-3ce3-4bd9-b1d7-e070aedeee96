# app/core/config.py
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn
from typing import Optional, List
import secrets
import os

class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "ORBIS Suivi Travaux SAAS"
    
    # Supabase Configuration
    SUPABASE_URL: str = "https://dkmyxkkokwuxopahokcd.supabase.co"
    SUPABASE_ANON_KEY: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRrbXl4a2tva3d1eG9wYWhva2NkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ2MDQzNTEsImV4cCI6MjA1MDE4MDM1MX0.T--dqBJLQpPLYeH8_sE0zL5nWEU96xYH_8_8hKQ2c4w"
    SUPABASE_SERVICE_ROLE_KEY: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRrbXl4a2tva3d1eG9wYWhva2NkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDYwNDM1MSwiZXhwIjoyMDUwMTgwMzUxfQ.Ps8LpN0R9x6Uw23V0CHpFn_r7WHU7yfwNkgRJqfN5s8"
    
    # Database - PostgreSQL with Supabase
    DATABASE_URL: str = "****************************************************************************/postgres"
    # AsyncPG version for SQLAlchemy async operations
    ASYNC_DATABASE_URL: str = "postgresql+asyncpg://postgres:E5FACUUHRbaX&@db.dkmyxkkokwuxopahokcd.supabase.co:5432/postgres"
    DATABASE_ECHO: bool = False
    
    # Security
    SECRET_KEY: str = os.getenv("SECRET_KEY", secrets.token_urlsafe(32))
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # CORS - Updated with current frontend URLs
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8000",
        "https://localhost:3000",
        "https://localhost:8000",
        "https://orbis-suivi-travaux-83ebvd-u2nimd-8e2b45.mgx.dev",
        "https://orbis-frontend-83ebvd-u2nimd-99a156.mgx.dev",
        "https://orbis-frontend-83ebvd-u2nimd-aa3609.mgx.dev",
    ]
    
    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: str = "redis://localhost:6379"
    
    # File Storage
    UPLOAD_FOLDER: str = "uploads"
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    
    # Multi-tenant
    MAX_COMPANIES: int = 10
    MAX_RECORDS_PER_ENTITY: int = 999999
    
    # Environment
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    DEBUG: bool = os.getenv("DEBUG", "true").lower() == "true"
    
    class Config:
        case_sensitive = True
        env_file = ".env"

settings = Settings()